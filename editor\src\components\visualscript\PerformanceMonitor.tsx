/**
 * PerformanceMonitor.tsx
 * 
 * 性能监控组件
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Statistic,
  Progress,
  Table,
  Button,
  Switch,
  Space,
  Typography,
  Alert,
  Tooltip,
  Badge,
  Divider,
  Row,
  Col,
  Tag
} from 'antd';
import {
  DashboardOutlined,
  ThunderboltOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  StopOutlined,
  PlayCircleOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { Line } from '@ant-design/plots';

const { Title, Text } = Typography;

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  timestamp: number;
  fps: number;
  memoryUsage: number;
  cpuUsage: number;
  nodeCount: number;
  connectionCount: number;
  renderTime: number;
  updateTime: number;
  networkLatency: number;
}

/**
 * 性能警告接口
 */
interface PerformanceWarning {
  id: string;
  type: 'memory' | 'cpu' | 'fps' | 'network';
  level: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  value: number;
  threshold: number;
}

/**
 * 性能监控属性
 */
interface PerformanceMonitorProps {
  scriptId: string;
  isExecuting: boolean;
  onOptimizationSuggestion?: (suggestions: string[]) => void;
}

/**
 * 性能监控组件
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  scriptId,
  isExecuting,
  onOptimizationSuggestion
}) => {
  const { t } = useTranslation();
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [currentMetrics, setCurrentMetrics] = useState<PerformanceMetrics | null>(null);
  const [warnings, setWarnings] = useState<PerformanceWarning[]>([]);
  const [autoOptimize, setAutoOptimize] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const frameRef = useRef<number | null>(null);
  const lastFrameTime = useRef<number>(0);
  const frameCount = useRef<number>(0);

  // 性能阈值配置
  const thresholds = {
    fps: { warning: 30, error: 15, critical: 10 },
    memory: { warning: 100, error: 200, critical: 500 }, // MB
    cpu: { warning: 70, error: 85, critical: 95 }, // %
    network: { warning: 100, error: 200, critical: 500 } // ms
  };

  // 开始监控
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    
    // 启动性能数据收集
    intervalRef.current = setInterval(() => {
      collectPerformanceData();
    }, 1000);

    // 启动FPS监控
    const measureFPS = () => {
      const now = performance.now();
      frameCount.current++;
      
      if (now - lastFrameTime.current >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / (now - lastFrameTime.current));
        frameCount.current = 0;
        lastFrameTime.current = now;
        
        // 更新FPS数据
        setCurrentMetrics(prev => prev ? { ...prev, fps } : null);
      }
      
      if (isMonitoring) {
        frameRef.current = requestAnimationFrame(measureFPS);
      }
    };
    
    frameRef.current = requestAnimationFrame(measureFPS);
  }, [isMonitoring]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    
    if (frameRef.current) {
      cancelAnimationFrame(frameRef.current);
      frameRef.current = null;
    }
  }, []);

  // 收集性能数据
  const collectPerformanceData = useCallback(() => {
    const now = Date.now();
    
    // 获取内存使用情况
    const memoryInfo = (performance as any).memory;
    const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : 0;
    
    // 模拟CPU使用率（实际应用中需要更精确的测量）
    const cpuUsage = Math.random() * 20 + 40; // 40-60%的模拟值
    
    // 获取网络延迟（如果有WebSocket连接）
    const networkLatency = Math.random() * 50 + 20; // 20-70ms的模拟值
    
    // 获取渲染时间
    const renderTime = performance.now() % 16.67; // 模拟渲染时间
    const updateTime = performance.now() % 8.33; // 模拟更新时间
    
    const newMetrics: PerformanceMetrics = {
      timestamp: now,
      fps: currentMetrics?.fps || 60,
      memoryUsage,
      cpuUsage,
      nodeCount: 0, // 需要从脚本数据获取
      connectionCount: 0, // 需要从脚本数据获取
      renderTime,
      updateTime,
      networkLatency
    };

    setCurrentMetrics(newMetrics);
    setMetrics(prev => [...prev.slice(-59), newMetrics]); // 保留最近60个数据点

    // 检查性能警告
    checkPerformanceWarnings(newMetrics);
  }, [currentMetrics]);

  // 检查性能警告
  const checkPerformanceWarnings = useCallback((metrics: PerformanceMetrics) => {
    const newWarnings: PerformanceWarning[] = [];

    // 检查FPS
    if (metrics.fps < thresholds.fps.critical) {
      newWarnings.push({
        id: `fps_${Date.now()}`,
        type: 'fps',
        level: 'critical',
        message: t('性能监控.FPS过低'),
        timestamp: metrics.timestamp,
        value: metrics.fps,
        threshold: thresholds.fps.critical
      });
    } else if (metrics.fps < thresholds.fps.error) {
      newWarnings.push({
        id: `fps_${Date.now()}`,
        type: 'fps',
        level: 'error',
        message: t('性能监控.FPS较低'),
        timestamp: metrics.timestamp,
        value: metrics.fps,
        threshold: thresholds.fps.error
      });
    }

    // 检查内存使用
    if (metrics.memoryUsage > thresholds.memory.critical) {
      newWarnings.push({
        id: `memory_${Date.now()}`,
        type: 'memory',
        level: 'critical',
        message: t('性能监控.内存使用过高'),
        timestamp: metrics.timestamp,
        value: metrics.memoryUsage,
        threshold: thresholds.memory.critical
      });
    }

    // 检查CPU使用
    if (metrics.cpuUsage > thresholds.cpu.error) {
      newWarnings.push({
        id: `cpu_${Date.now()}`,
        type: 'cpu',
        level: 'error',
        message: t('性能监控.CPU使用过高'),
        timestamp: metrics.timestamp,
        value: metrics.cpuUsage,
        threshold: thresholds.cpu.error
      });
    }

    if (newWarnings.length > 0) {
      setWarnings(prev => [...newWarnings, ...prev.slice(0, 19)]); // 保留最近20个警告
      
      // 如果启用自动优化，生成优化建议
      if (autoOptimize && onOptimizationSuggestion) {
        const suggestions = generateOptimizationSuggestions(newWarnings);
        onOptimizationSuggestion(suggestions);
      }
    }
  }, [autoOptimize, onOptimizationSuggestion, t]);

  // 生成优化建议
  const generateOptimizationSuggestions = (warnings: PerformanceWarning[]): string[] => {
    const suggestions: string[] = [];

    warnings.forEach(warning => {
      switch (warning.type) {
        case 'fps':
          suggestions.push(t('性能监控.建议减少节点数量'));
          suggestions.push(t('性能监控.建议优化渲染逻辑'));
          break;
        case 'memory':
          suggestions.push(t('性能监控.建议清理未使用的对象'));
          suggestions.push(t('性能监控.建议减少数据缓存'));
          break;
        case 'cpu':
          suggestions.push(t('性能监控.建议优化计算逻辑'));
          suggestions.push(t('性能监控.建议使用异步处理'));
          break;
        case 'network':
          suggestions.push(t('性能监控.建议优化网络请求'));
          suggestions.push(t('性能监控.建议使用数据压缩'));
          break;
      }
    });

    return [...new Set(suggestions)]; // 去重
  };

  // 导出性能报告
  const exportPerformanceReport = useCallback(() => {
    const report = {
      scriptId,
      timestamp: new Date().toISOString(),
      metrics: metrics,
      warnings: warnings,
      summary: {
        avgFPS: metrics.reduce((sum, m) => sum + m.fps, 0) / metrics.length,
        maxMemory: Math.max(...metrics.map(m => m.memoryUsage)),
        avgCPU: metrics.reduce((sum, m) => sum + m.cpuUsage, 0) / metrics.length,
        warningCount: warnings.length
      }
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-report-${scriptId}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [scriptId, metrics, warnings]);

  // 清除警告
  const clearWarnings = useCallback(() => {
    setWarnings([]);
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopMonitoring();
    };
  }, [stopMonitoring]);

  // 准备图表数据
  const chartData = metrics.map(m => [
    { time: new Date(m.timestamp).toLocaleTimeString(), value: m.fps, type: 'FPS' },
    { time: new Date(m.timestamp).toLocaleTimeString(), value: m.memoryUsage, type: 'Memory (MB)' },
    { time: new Date(m.timestamp).toLocaleTimeString(), value: m.cpuUsage, type: 'CPU (%)' }
  ]).flat();

  const chartConfig = {
    data: chartData,
    xField: 'time',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
  };

  return (
    <Card
      title={
        <Space>
          <DashboardOutlined />
          <span>{t('性能监控.性能监控')}</span>
          <Badge 
            status={isMonitoring ? 'processing' : 'default'} 
            text={isMonitoring ? t('性能监控.监控中') : t('性能监控.已停止')}
          />
        </Space>
      }
      extra={
        <Space>
          <Switch
            checked={autoOptimize}
            onChange={setAutoOptimize}
            checkedChildren={t('性能监控.自动优化')}
            unCheckedChildren={t('性能监控.手动优化')}
          />
          <Button
            type="primary"
            danger={isMonitoring}
            icon={isMonitoring ? <StopOutlined /> : <PlayCircleOutlined />}
            onClick={isMonitoring ? stopMonitoring : startMonitoring}
          >
            {isMonitoring ? t('性能监控.停止监控') : t('性能监控.开始监控')}
          </Button>
          <Button
            icon={<DownloadOutlined />}
            onClick={exportPerformanceReport}
            disabled={metrics.length === 0}
          >
            {t('性能监控.导出报告')}
          </Button>
        </Space>
      }
      style={{ height: '100%' }}
      styles={{ body: { padding: '16px', height: 'calc(100% - 60px)', overflow: 'auto' } }}
    >
      {/* 性能警告 */}
      {warnings.length > 0 && (
        <Alert
          type="warning"
          showIcon
          message={t('性能监控.性能警告')}
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              {warnings.slice(0, 3).map(warning => (
                <Text key={warning.id} type={warning.level === 'critical' ? 'danger' : 'warning'}>
                  {warning.message} ({warning.value.toFixed(1)} &gt; {warning.threshold})
                </Text>
              ))}
              {warnings.length > 3 && (
                <Text type="secondary">
                  {t('性能监控.还有')} {warnings.length - 3} {t('性能监控.个警告')}
                </Text>
              )}
            </Space>
          }
          action={
            <Button size="small" onClick={clearWarnings}>
              {t('性能监控.清除')}
            </Button>
          }
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 实时指标 */}
      {currentMetrics && (
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col span={6}>
            <Statistic
              title="FPS"
              value={currentMetrics.fps}
              precision={0}
              valueStyle={{ 
                color: currentMetrics.fps < 30 ? '#cf1322' : '#3f8600' 
              }}
              prefix={<ThunderboltOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('性能监控.内存使用')}
              value={currentMetrics.memoryUsage}
              precision={1}
              suffix="MB"
              valueStyle={{ 
                color: currentMetrics.memoryUsage > 200 ? '#cf1322' : '#3f8600' 
              }}
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="CPU"
              value={currentMetrics.cpuUsage}
              precision={1}
              suffix="%"
              valueStyle={{ 
                color: currentMetrics.cpuUsage > 80 ? '#cf1322' : '#3f8600' 
              }}
              prefix={<DashboardOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title={t('性能监控.网络延迟')}
              value={currentMetrics.networkLatency}
              precision={0}
              suffix="ms"
              valueStyle={{ 
                color: currentMetrics.networkLatency > 100 ? '#cf1322' : '#3f8600' 
              }}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>
      )}

      {/* 性能图表 */}
      {metrics.length > 0 && (
        <div style={{ marginBottom: '16px' }}>
          <Title level={5}>{t('性能监控.性能趋势')}</Title>
          <Line {...chartConfig} height={200} />
        </div>
      )}

      <Divider />

      {/* 性能建议 */}
      <div>
        <Title level={5}>{t('性能监控.优化建议')}</Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Tag color="blue">{t('性能监控.减少不必要的节点连接')}</Tag>
          <Tag color="green">{t('性能监控.使用节点缓存机制')}</Tag>
          <Tag color="orange">{t('性能监控.优化复杂计算节点')}</Tag>
          <Tag color="purple">{t('性能监控.启用渲染优化')}</Tag>
        </Space>
      </div>
    </Card>
  );
};

export default PerformanceMonitor;
