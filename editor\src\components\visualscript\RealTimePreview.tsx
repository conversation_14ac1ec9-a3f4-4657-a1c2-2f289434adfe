/**
 * 实时预览组件
 * 提供视觉脚本的实时预览和调试功能
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Space,
  Tooltip,
  Switch,
  Slider,
  Divider,
  Typography,
  Progress,
  Badge,
  Tabs,
  List,
  Tag,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  BugOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ThunderboltOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Text } = Typography;

// 执行状态枚举
enum ExecutionState {
  STOPPED = 'stopped',
  RUNNING = 'running',
  PAUSED = 'paused',
  ERROR = 'error'
}

// 节点执行信息接口
interface NodeExecutionInfo {
  nodeId: string;
  nodeName: string;
  nodeType: string;
  executionTime: number;
  executionCount: number;
  lastExecuted: Date;
  status: 'success' | 'error' | 'warning';
  output?: any;
  error?: string;
}

// 性能统计接口
interface PerformanceStats {
  totalExecutionTime: number;
  averageExecutionTime: number;
  nodeExecutionCount: number;
  memoryUsage: number;
  fps: number;
  errorCount: number;
  warningCount: number;
}

// 组件属性接口
interface RealTimePreviewProps {
  scriptData: any;
  isExecuting: boolean;
  onExecutionStateChange: (state: ExecutionState) => void;
  onNodeSelect?: (nodeId: string) => void;
  width?: number;
  height?: number;
}

/**
 * 实时预览组件
 */
const RealTimePreview: React.FC<RealTimePreviewProps> = ({
  scriptData: _scriptData,
  isExecuting,
  onExecutionStateChange,
  onNodeSelect,
  width = 400,
  height = 300
}) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();

  // 状态管理
  const [executionState, setExecutionState] = useState<ExecutionState>(ExecutionState.STOPPED);
  const [previewEnabled, setPreviewEnabled] = useState(true);
  const [debugMode, setDebugMode] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [executionSpeed, setExecutionSpeed] = useState(1.0);
  const [nodeExecutions, setNodeExecutions] = useState<NodeExecutionInfo[]>([]);
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats>({
    totalExecutionTime: 0,
    averageExecutionTime: 0,
    nodeExecutionCount: 0,
    memoryUsage: 0,
    fps: 0,
    errorCount: 0,
    warningCount: 0
  });
  const [executionLog, setExecutionLog] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('preview');

  // 同步执行状态
  useEffect(() => {
    if (isExecuting && executionState === ExecutionState.STOPPED) {
      setExecutionState(ExecutionState.RUNNING);
    } else if (!isExecuting && executionState === ExecutionState.RUNNING) {
      setExecutionState(ExecutionState.STOPPED);
    }
  }, [isExecuting, executionState]);

  // 开始执行
  const handleStart = useCallback(() => {
    setExecutionState(ExecutionState.RUNNING);
    onExecutionStateChange(ExecutionState.RUNNING);
    addToLog('脚本开始执行');
  }, [onExecutionStateChange]);

  // 暂停执行
  const handlePause = useCallback(() => {
    setExecutionState(ExecutionState.PAUSED);
    onExecutionStateChange(ExecutionState.PAUSED);
    addToLog('脚本已暂停');
  }, [onExecutionStateChange]);

  // 停止执行
  const handleStop = useCallback(() => {
    setExecutionState(ExecutionState.STOPPED);
    onExecutionStateChange(ExecutionState.STOPPED);
    addToLog('脚本已停止');
    
    // 清理动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, [onExecutionStateChange]);

  // 重新开始
  const handleRestart = useCallback(() => {
    handleStop();
    setTimeout(() => {
      handleStart();
    }, 100);
  }, [handleStart, handleStop]);

  // 添加日志
  const addToLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setExecutionLog(prev => [...prev.slice(-99), `[${timestamp}] ${message}`]);
  }, []);



  // 渲染预览画布
  const renderPreview = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !previewEnabled) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 绘制网格
    if (debugMode) {
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      
      for (let x = 0; x < canvas.width; x += 20) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
      }
      
      for (let y = 0; y < canvas.height; y += 20) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }
    }

    // 模拟脚本执行结果的可视化
    if (executionState === ExecutionState.RUNNING) {
      const time = Date.now() * 0.001 * executionSpeed;
      
      // 绘制动态元素
      ctx.fillStyle = '#1890ff';
      const x = (Math.sin(time) * 0.5 + 0.5) * (canvas.width - 40) + 20;
      const y = (Math.cos(time * 0.7) * 0.5 + 0.5) * (canvas.height - 40) + 20;
      ctx.fillRect(x - 10, y - 10, 20, 20);

      // 绘制轨迹
      ctx.strokeStyle = '#52c41a';
      ctx.lineWidth = 2;
      ctx.beginPath();
      for (let i = 0; i < 100; i++) {
        const t = time - i * 0.01;
        const px = (Math.sin(t) * 0.5 + 0.5) * (canvas.width - 40) + 20;
        const py = (Math.cos(t * 0.7) * 0.5 + 0.5) * (canvas.height - 40) + 20;
        if (i === 0) {
          ctx.moveTo(px, py);
        } else {
          ctx.lineTo(px, py);
        }
      }
      ctx.stroke();
    }

    // 绘制状态信息
    if (debugMode) {
      ctx.fillStyle = '#000';
      ctx.font = '12px Arial';
      ctx.fillText(`状态: ${executionState}`, 10, 20);
      ctx.fillText(`速度: ${executionSpeed}x`, 10, 35);
      ctx.fillText(`FPS: ${performanceStats.fps.toFixed(1)}`, 10, 50);
    }

    // 继续动画循环
    if (executionState === ExecutionState.RUNNING) {
      animationFrameRef.current = requestAnimationFrame(renderPreview);
    }
  }, [previewEnabled, debugMode, executionState, executionSpeed, performanceStats.fps]);

  // 启动渲染循环
  useEffect(() => {
    if (executionState === ExecutionState.RUNNING && previewEnabled) {
      renderPreview();
    }
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [executionState, previewEnabled, renderPreview]);

  // 性能监控
  useEffect(() => {
    const interval = setInterval(() => {
      if (executionState === ExecutionState.RUNNING) {
        setPerformanceStats(prev => ({
          ...prev,
          fps: Math.random() * 60 + 30, // 模拟FPS
          memoryUsage: Math.random() * 100 + 50, // 模拟内存使用
          nodeExecutionCount: prev.nodeExecutionCount + Math.floor(Math.random() * 5)
        }));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [executionState]);

  // 渲染控制面板
  const renderControls = () => (
    <Space>
      {executionState === ExecutionState.RUNNING ? (
        <Button
          icon={<PauseCircleOutlined />}
          onClick={handlePause}
          type="primary"
        >
          {t('预览.暂停')}
        </Button>
      ) : (
        <Button
          icon={<PlayCircleOutlined />}
          onClick={handleStart}
          type="primary"
        >
          {t('预览.开始')}
        </Button>
      )}
      
      <Button
        icon={<StopOutlined />}
        onClick={handleStop}
        disabled={executionState === ExecutionState.STOPPED}
      >
        {t('预览.停止')}
      </Button>
      
      <Button
        icon={<ReloadOutlined />}
        onClick={handleRestart}
      >
        {t('预览.重启')}
      </Button>
      
      <Divider type="vertical" />
      
      <Tooltip title={t('预览.调试模式')}>
        <Switch
          checked={debugMode}
          onChange={setDebugMode}
          checkedChildren={<BugOutlined />}
          unCheckedChildren={<BugOutlined />}
        />
      </Tooltip>
      
      <Tooltip title={previewEnabled ? t('预览.隐藏预览') : t('预览.显示预览')}>
        <Switch
          checked={previewEnabled}
          onChange={setPreviewEnabled}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
        />
      </Tooltip>
      
      <Tooltip title={fullscreen ? t('预览.退出全屏') : t('预览.全屏')}>
        <Button
          icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
          onClick={() => setFullscreen(!fullscreen)}
        />
      </Tooltip>
    </Space>
  );

  // 渲染性能统计
  const renderPerformanceStats = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Statistic
          title={t('预览.FPS')}
          value={performanceStats.fps}
          precision={1}
          prefix={<ThunderboltOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title={t('预览.内存使用')}
          value={performanceStats.memoryUsage}
          suffix="MB"
        />
      </Col>
      <Col span={6}>
        <Statistic
          title={t('预览.节点执行')}
          value={performanceStats.nodeExecutionCount}
          prefix={<CheckCircleOutlined />}
        />
      </Col>
      <Col span={6}>
        <Statistic
          title={t('预览.错误数')}
          value={performanceStats.errorCount}
          prefix={<WarningOutlined />}
        />
      </Col>
    </Row>
  );

  // 渲染节点执行列表
  const renderNodeExecutions = () => (
    <List
      size="small"
      dataSource={nodeExecutions}
      renderItem={item => (
        <List.Item
          actions={[
            <Button
              type="link"
              size="small"
              onClick={() => onNodeSelect?.(item.nodeId)}
            >
              {t('预览.定位')}
            </Button>
          ]}
        >
          <List.Item.Meta
            title={
              <Space>
                <span>{item.nodeName}</span>
                <Badge
                  status={item.status === 'success' ? 'success' : 
                          item.status === 'error' ? 'error' : 'warning'}
                />
                <Tag>{item.nodeType}</Tag>
              </Space>
            }
            description={
              <Space>
                <Text type="secondary">
                  {t('预览.执行时间')}: {item.executionTime.toFixed(2)}ms
                </Text>
                <Text type="secondary">
                  {t('预览.执行次数')}: {item.executionCount}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );

  return (
    <Card
      title={t('预览.实时预览')}
      extra={renderControls()}
      style={{ width: fullscreen ? '100vw' : width, height: fullscreen ? '100vh' : height }}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'preview',
            label: t('预览.预览'),
            children: (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Space>
                    <Text>{t('预览.执行速度')}:</Text>
                    <Slider
                      min={0.1}
                      max={3.0}
                      step={0.1}
                      value={executionSpeed}
                      onChange={setExecutionSpeed}
                      style={{ width: 100 }}
                    />
                    <Text>{executionSpeed}x</Text>
                  </Space>
                </div>

                <canvas
                  ref={canvasRef}
                  width={width - 48}
                  height={height - 200}
                  style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: 4,
                    backgroundColor: previewEnabled ? '#fff' : '#f5f5f5'
                  }}
                />
              </div>
            )
          },
          {
            key: 'performance',
            label: t('预览.性能'),
            children: (
              <div>
                {renderPerformanceStats()}
                <Divider />
                <Progress
                  percent={Math.min(100, performanceStats.memoryUsage)}
                  status={performanceStats.memoryUsage > 80 ? 'exception' : 'active'}
                  format={() => `${performanceStats.memoryUsage.toFixed(1)}MB`}
                />
              </div>
            )
          },
          {
            key: 'nodes',
            label: t('预览.节点执行'),
            children: renderNodeExecutions()
          },
          {
            key: 'logs',
            label: t('预览.日志'),
            children: (
              <div style={{ height: 200, overflow: 'auto', backgroundColor: '#f5f5f5', padding: 8 }}>
                {executionLog.map((log, index) => (
                  <div key={index} style={{ fontFamily: 'monospace', fontSize: 12 }}>
                    {log}
                  </div>
                ))}
              </div>
            )
          }
        ]}
      />
    </Card>
  );
};

export default RealTimePreview;
