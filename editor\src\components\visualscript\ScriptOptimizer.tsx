/**
 * ScriptOptimizer.tsx
 * 
 * 脚本优化器组件
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  Card,
  Button,
  Progress,
  List,
  Space,
  Typography,
  Alert,
  Checkbox,
  Divider,
  Statistic,
  Row,
  Col,
  Tag,
  Tooltip,
  Modal,
  Steps,
  Result
} from 'antd';
import {
  ThunderboltOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  OptimizationOutlined,
  BugOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

/**
 * 优化建议接口
 */
interface OptimizationSuggestion {
  id: string;
  type: 'performance' | 'memory' | 'structure' | 'best_practice';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  autoFixable: boolean;
  nodeIds?: string[];
  beforeValue?: any;
  afterValue?: any;
}

/**
 * 优化结果接口
 */
interface OptimizationResult {
  suggestion: OptimizationSuggestion;
  applied: boolean;
  success: boolean;
  error?: string;
  metrics?: {
    nodeCountBefore: number;
    nodeCountAfter: number;
    connectionCountBefore: number;
    connectionCountAfter: number;
    performanceImprovement: number;
  };
}

/**
 * 脚本优化器属性
 */
interface ScriptOptimizerProps {
  scriptData: any;
  onScriptChange: (newScriptData: any) => void;
  onOptimizationComplete?: (results: OptimizationResult[]) => void;
}

/**
 * 脚本优化器组件
 */
const ScriptOptimizer: React.FC<ScriptOptimizerProps> = ({
  scriptData,
  onScriptChange,
  onOptimizationComplete
}) => {
  const { t } = useTranslation();
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizationProgress, setOptimizationProgress] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [optimizationResults, setOptimizationResults] = useState<OptimizationResult[]>([]);
  const [currentStep, setCurrentStep] = useState(0);

  // 分析脚本并生成优化建议
  const analyzeScript = useCallback(async () => {
    setIsAnalyzing(true);
    setCurrentStep(0);

    try {
      // 模拟分析过程
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockSuggestions: OptimizationSuggestion[] = [
        {
          id: 'opt_1',
          type: 'performance',
          severity: 'high',
          title: t('优化器.移除未使用的节点'),
          description: t('优化器.发现了一些未连接的节点，移除它们可以提高性能'),
          impact: t('优化器.减少内存使用和提高执行速度'),
          effort: 'low',
          autoFixable: true,
          nodeIds: ['node_1', 'node_2'],
          beforeValue: { nodeCount: 50 },
          afterValue: { nodeCount: 48 }
        },
        {
          id: 'opt_2',
          type: 'structure',
          severity: 'medium',
          title: t('优化器.合并重复的计算节点'),
          description: t('优化器.发现了重复的数学计算，可以合并为一个节点'),
          impact: t('优化器.减少计算复杂度'),
          effort: 'medium',
          autoFixable: true,
          nodeIds: ['node_3', 'node_4', 'node_5']
        },
        {
          id: 'opt_3',
          type: 'best_practice',
          severity: 'low',
          title: t('优化器.添加错误处理节点'),
          description: t('优化器.建议在关键路径上添加错误处理'),
          impact: t('优化器.提高脚本稳定性'),
          effort: 'high',
          autoFixable: false
        },
        {
          id: 'opt_4',
          type: 'memory',
          severity: 'medium',
          title: t('优化器.优化大数据处理'),
          description: t('优化器.使用流式处理替代一次性加载大量数据'),
          impact: t('优化器.显著减少内存占用'),
          effort: 'high',
          autoFixable: false
        }
      ];

      setSuggestions(mockSuggestions);
      setCurrentStep(1);
    } catch (error) {
      console.error('分析失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [t]);

  // 应用优化建议
  const applyOptimizations = useCallback(async () => {
    if (selectedSuggestions.length === 0) return;

    setIsOptimizing(true);
    setOptimizationProgress(0);
    setCurrentStep(2);

    const results: OptimizationResult[] = [];
    const selectedSuggestionObjects = suggestions.filter(s => selectedSuggestions.includes(s.id));

    for (let i = 0; i < selectedSuggestionObjects.length; i++) {
      const suggestion = selectedSuggestionObjects[i];
      
      try {
        // 模拟优化过程
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟应用优化
        const success = suggestion.autoFixable ? Math.random() > 0.1 : false;
        
        const result: OptimizationResult = {
          suggestion,
          applied: true,
          success,
          metrics: success ? {
            nodeCountBefore: 50,
            nodeCountAfter: 48,
            connectionCountBefore: 75,
            connectionCountAfter: 70,
            performanceImprovement: Math.random() * 30 + 10
          } : undefined
        };

        if (!success && suggestion.autoFixable) {
          result.error = t('优化器.自动修复失败，需要手动处理');
        }

        results.push(result);
        setOptimizationProgress(((i + 1) / selectedSuggestionObjects.length) * 100);
      } catch (error) {
        results.push({
          suggestion,
          applied: false,
          success: false,
          error: error instanceof Error ? error.message : t('优化器.未知错误')
        });
      }
    }

    setOptimizationResults(results);
    setCurrentStep(3);
    setIsOptimizing(false);
    setShowResults(true);

    if (onOptimizationComplete) {
      onOptimizationComplete(results);
    }
  }, [selectedSuggestions, suggestions, onOptimizationComplete, t]);

  // 获取严重程度颜色
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'red';
      case 'high': return 'orange';
      case 'medium': return 'yellow';
      case 'low': return 'blue';
      default: return 'default';
    }
  };

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'performance': return <ThunderboltOutlined />;
      case 'memory': return <SettingOutlined />;
      case 'structure': return <FileTextOutlined />;
      case 'best_practice': return <CheckCircleOutlined />;
      default: return <InfoCircleOutlined />;
    }
  };

  // 初始化时自动分析
  useEffect(() => {
    if (scriptData && scriptData.nodes && scriptData.nodes.length > 0) {
      analyzeScript();
    }
  }, [scriptData, analyzeScript]);

  return (
    <Card
      title={
        <Space>
          <OptimizationOutlined />
          <span>{t('优化器.脚本优化器')}</span>
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<BugOutlined />}
            onClick={analyzeScript}
            loading={isAnalyzing}
          >
            {t('优化器.重新分析')}
          </Button>
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            onClick={applyOptimizations}
            disabled={selectedSuggestions.length === 0 || isOptimizing}
            loading={isOptimizing}
          >
            {t('优化器.应用优化')} ({selectedSuggestions.length})
          </Button>
        </Space>
      }
      style={{ height: '100%' }}
      bodyStyle={{ padding: '16px', height: 'calc(100% - 60px)', overflow: 'auto' }}
    >
      {/* 优化步骤 */}
      <Steps current={currentStep} style={{ marginBottom: '24px' }}>
        <Step title={t('优化器.分析脚本')} icon={<BugOutlined />} />
        <Step title={t('优化器.选择建议')} icon={<CheckCircleOutlined />} />
        <Step title={t('优化器.应用优化')} icon={<PlayCircleOutlined />} />
        <Step title={t('优化器.查看结果')} icon={<FileTextOutlined />} />
      </Steps>

      {/* 脚本统计 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Statistic
            title={t('优化器.节点数量')}
            value={scriptData?.nodes?.length || 0}
            prefix={<FileTextOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('优化器.连接数量')}
            value={scriptData?.connections?.length || 0}
            prefix={<SettingOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('优化器.优化建议')}
            value={suggestions.length}
            prefix={<OptimizationOutlined />}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title={t('优化器.可自动修复')}
            value={suggestions.filter(s => s.autoFixable).length}
            prefix={<ThunderboltOutlined />}
          />
        </Col>
      </Row>

      {/* 优化进度 */}
      {isOptimizing && (
        <Alert
          type="info"
          showIcon
          message={t('优化器.正在应用优化')}
          description={
            <Progress
              percent={optimizationProgress}
              status="active"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          }
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 优化建议列表 */}
      {suggestions.length > 0 && (
        <div>
          <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={5}>{t('优化器.优化建议')}</Title>
            <Space>
              <Button
                size="small"
                onClick={() => setSelectedSuggestions(suggestions.map(s => s.id))}
              >
                {t('优化器.全选')}
              </Button>
              <Button
                size="small"
                onClick={() => setSelectedSuggestions([])}
              >
                {t('优化器.清除')}
              </Button>
              <Button
                size="small"
                onClick={() => setSelectedSuggestions(suggestions.filter(s => s.autoFixable).map(s => s.id))}
              >
                {t('优化器.选择可自动修复')}
              </Button>
            </Space>
          </div>

          <List
            dataSource={suggestions}
            renderItem={(suggestion) => (
              <List.Item
                key={suggestion.id}
                style={{
                  border: '1px solid #f0f0f0',
                  borderRadius: '6px',
                  marginBottom: '8px',
                  padding: '16px'
                }}
              >
                <List.Item.Meta
                  avatar={
                    <Checkbox
                      checked={selectedSuggestions.includes(suggestion.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedSuggestions(prev => [...prev, suggestion.id]);
                        } else {
                          setSelectedSuggestions(prev => prev.filter(id => id !== suggestion.id));
                        }
                      }}
                    />
                  }
                  title={
                    <Space>
                      {getTypeIcon(suggestion.type)}
                      <span>{suggestion.title}</span>
                      <Tag color={getSeverityColor(suggestion.severity)}>
                        {suggestion.severity.toUpperCase()}
                      </Tag>
                      {suggestion.autoFixable && (
                        <Tag color="green">{t('优化器.可自动修复')}</Tag>
                      )}
                    </Space>
                  }
                  description={
                    <div>
                      <Paragraph style={{ marginBottom: '8px' }}>
                        {suggestion.description}
                      </Paragraph>
                      <Space>
                        <Text strong>{t('优化器.影响')}:</Text>
                        <Text>{suggestion.impact}</Text>
                      </Space>
                      <br />
                      <Space>
                        <Text strong>{t('优化器.工作量')}:</Text>
                        <Tag color={suggestion.effort === 'low' ? 'green' : suggestion.effort === 'medium' ? 'orange' : 'red'}>
                          {suggestion.effort.toUpperCase()}
                        </Tag>
                      </Space>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </div>
      )}

      {/* 优化结果模态框 */}
      <Modal
        title={t('优化器.优化结果')}
        open={showResults}
        onCancel={() => setShowResults(false)}
        footer={[
          <Button key="close" onClick={() => setShowResults(false)}>
            {t('优化器.关闭')}
          </Button>
        ]}
        width={800}
      >
        <Result
          status="success"
          title={t('优化器.优化完成')}
          subTitle={`${t('优化器.成功应用')} ${optimizationResults.filter(r => r.success).length} ${t('优化器.个优化建议')}`}
        />
        
        <List
          dataSource={optimizationResults}
          renderItem={(result) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  result.success ? 
                    <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                    <WarningOutlined style={{ color: '#faad14' }} />
                }
                title={result.suggestion.title}
                description={
                  <div>
                    {result.success ? (
                      <Text type="success">{t('优化器.应用成功')}</Text>
                    ) : (
                      <Text type="warning">{result.error || t('优化器.应用失败')}</Text>
                    )}
                    {result.metrics && (
                      <div style={{ marginTop: '8px' }}>
                        <Text>{t('优化器.性能提升')}: {result.metrics.performanceImprovement.toFixed(1)}%</Text>
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Modal>
    </Card>
  );
};

export default ScriptOptimizer;
