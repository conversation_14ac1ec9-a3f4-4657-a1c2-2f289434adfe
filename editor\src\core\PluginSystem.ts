/**
 * PluginSystem.ts
 * 
 * 组件插件系统核心架构
 */

import { EventEmitter } from '../utils/EventEmitter';

/**
 * 插件接口
 */
export interface Plugin {
  /** 插件唯一标识 */
  id: string;
  /** 插件名称 */
  name: string;
  /** 插件版本 */
  version: string;
  /** 插件描述 */
  description: string;
  /** 插件作者 */
  author: string;
  /** 插件依赖 */
  dependencies?: string[];
  /** 插件入口点 */
  main: string;
  /** 插件配置 */
  config?: Record<string, any>;
  /** 插件生命周期钩子 */
  hooks?: PluginHooks;
  /** 插件是否启用 */
  enabled: boolean;
}

/**
 * 插件生命周期钩子
 */
export interface PluginHooks {
  /** 插件安装时调用 */
  onInstall?: (context: PluginContext) => Promise<void> | void;
  /** 插件启用时调用 */
  onEnable?: (context: PluginContext) => Promise<void> | void;
  /** 插件禁用时调用 */
  onDisable?: (context: PluginContext) => Promise<void> | void;
  /** 插件卸载时调用 */
  onUninstall?: (context: PluginContext) => Promise<void> | void;
  /** 编辑器初始化时调用 */
  onEditorInit?: (context: PluginContext) => Promise<void> | void;
  /** 组件创建时调用 */
  onComponentCreate?: (context: PluginContext, component: any) => Promise<void> | void;
  /** 组件更新时调用 */
  onComponentUpdate?: (context: PluginContext, component: any) => Promise<void> | void;
  /** 组件删除时调用 */
  onComponentDelete?: (context: PluginContext, component: any) => Promise<void> | void;
}

/**
 * 插件上下文
 */
export interface PluginContext {
  /** 插件实例 */
  plugin: Plugin;
  /** 编辑器API */
  editor: EditorAPI;
  /** 工具函数 */
  utils: PluginUtils;
  /** 存储API */
  storage: PluginStorage;
  /** 日志记录器 */
  logger: PluginLogger;
}

/**
 * 编辑器API接口
 */
export interface EditorAPI {
  /** 注册组件类型 */
  registerComponent: (componentType: ComponentType) => void;
  /** 注册工具栏按钮 */
  registerToolbarButton: (button: ToolbarButton) => void;
  /** 注册面板 */
  registerPanel: (panel: Panel) => void;
  /** 注册菜单项 */
  registerMenuItem: (menuItem: MenuItem) => void;
  /** 注册快捷键 */
  registerShortcut: (shortcut: Shortcut) => void;
  /** 获取选中的组件 */
  getSelectedComponents: () => any[];
  /** 设置选中的组件 */
  setSelectedComponents: (components: any[]) => void;
  /** 添加组件 */
  addComponent: (component: any) => void;
  /** 更新组件 */
  updateComponent: (componentId: string, updates: any) => void;
  /** 删除组件 */
  deleteComponent: (componentId: string) => void;
  /** 显示通知 */
  showNotification: (message: string, type?: 'info' | 'success' | 'warning' | 'error') => void;
  /** 显示对话框 */
  showDialog: (options: DialogOptions) => Promise<any>;
}

/**
 * 组件类型定义
 */
export interface ComponentType {
  id: string;
  name: string;
  category: string;
  icon: string;
  defaultProps: Record<string, any>;
  render: (props: any) => React.ReactNode;
  propertyEditor?: React.ComponentType<any>;
  preview?: string;
}

/**
 * 工具栏按钮定义
 */
export interface ToolbarButton {
  id: string;
  label: string;
  icon: string;
  tooltip?: string;
  onClick: () => void;
  position?: 'left' | 'center' | 'right';
  group?: string;
}

/**
 * 面板定义
 */
export interface Panel {
  id: string;
  title: string;
  icon?: string;
  component: React.ComponentType<any>;
  position?: 'left' | 'right' | 'bottom';
  defaultSize?: number;
  resizable?: boolean;
}

/**
 * 菜单项定义
 */
export interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  onClick: () => void;
  submenu?: MenuItem[];
  separator?: boolean;
  position?: string;
}

/**
 * 快捷键定义
 */
export interface Shortcut {
  id: string;
  keys: string[];
  description: string;
  handler: () => void;
  scope?: string;
}

/**
 * 对话框选项
 */
export interface DialogOptions {
  title: string;
  content: React.ReactNode;
  width?: number;
  height?: number;
  buttons?: Array<{
    label: string;
    type?: 'primary' | 'default' | 'danger';
    onClick: () => void | Promise<void>;
  }>;
}

/**
 * 插件工具函数
 */
export interface PluginUtils {
  /** 生成唯一ID */
  generateId: () => string;
  /** 深度克隆对象 */
  deepClone: <T>(obj: T) => T;
  /** 防抖函数 */
  debounce: <T extends (...args: any[]) => any>(fn: T, delay: number) => T;
  /** 节流函数 */
  throttle: <T extends (...args: any[]) => any>(fn: T, delay: number) => T;
  /** 格式化日期 */
  formatDate: (date: Date, format: string) => string;
  /** 验证JSON */
  isValidJSON: (str: string) => boolean;
}

/**
 * 插件存储接口
 */
export interface PluginStorage {
  /** 获取数据 */
  get: <T = any>(key: string, defaultValue?: T) => T;
  /** 设置数据 */
  set: (key: string, value: any) => void;
  /** 删除数据 */
  remove: (key: string) => void;
  /** 清空数据 */
  clear: () => void;
  /** 获取所有键 */
  keys: () => string[];
}

/**
 * 插件日志记录器
 */
export interface PluginLogger {
  /** 信息日志 */
  info: (message: string, ...args: any[]) => void;
  /** 警告日志 */
  warn: (message: string, ...args: any[]) => void;
  /** 错误日志 */
  error: (message: string, ...args: any[]) => void;
  /** 调试日志 */
  debug: (message: string, ...args: any[]) => void;
}

/**
 * 插件系统主类
 */
export class PluginSystem extends EventEmitter {
  private static instance: PluginSystem;
  private plugins: Map<string, Plugin> = new Map();
  private loadedPlugins: Map<string, any> = new Map();
  private editorAPI: EditorAPI;
  private isInitialized = false;

  private constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): PluginSystem {
    if (!PluginSystem.instance) {
      PluginSystem.instance = new PluginSystem();
    }
    return PluginSystem.instance;
  }

  /**
   * 初始化插件系统
   */
  public async initialize(editorAPI: EditorAPI): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.editorAPI = editorAPI;
    this.isInitialized = true;

    // 加载已安装的插件
    await this.loadInstalledPlugins();

    this.emit('initialized');
  }

  /**
   * 安装插件
   */
  public async installPlugin(pluginData: Plugin | string): Promise<void> {
    try {
      let plugin: Plugin;

      if (typeof pluginData === 'string') {
        // 从URL或文件路径加载插件
        plugin = await this.loadPluginFromSource(pluginData);
      } else {
        plugin = pluginData;
      }

      // 验证插件
      this.validatePlugin(plugin);

      // 检查依赖
      await this.checkDependencies(plugin);

      // 安装插件
      this.plugins.set(plugin.id, plugin);

      // 调用安装钩子
      if (plugin.hooks?.onInstall) {
        const context = this.createPluginContext(plugin);
        await plugin.hooks.onInstall(context);
      }

      // 如果插件默认启用，则启用它
      if (plugin.enabled) {
        await this.enablePlugin(plugin.id);
      }

      this.emit('pluginInstalled', plugin);
    } catch (error) {
      throw new Error(`插件安装失败: ${error.message}`);
    }
  }

  /**
   * 卸载插件
   */
  public async uninstallPlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`);
    }

    try {
      // 先禁用插件
      if (plugin.enabled) {
        await this.disablePlugin(pluginId);
      }

      // 调用卸载钩子
      if (plugin.hooks?.onUninstall) {
        const context = this.createPluginContext(plugin);
        await plugin.hooks.onUninstall(context);
      }

      // 移除插件
      this.plugins.delete(pluginId);
      this.loadedPlugins.delete(pluginId);

      this.emit('pluginUninstalled', plugin);
    } catch (error) {
      throw new Error(`插件卸载失败: ${error.message}`);
    }
  }

  /**
   * 启用插件
   */
  public async enablePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`);
    }

    if (plugin.enabled) {
      return;
    }

    try {
      // 加载插件代码
      const pluginInstance = await this.loadPluginCode(plugin);
      this.loadedPlugins.set(pluginId, pluginInstance);

      // 调用启用钩子
      if (plugin.hooks?.onEnable) {
        const context = this.createPluginContext(plugin);
        await plugin.hooks.onEnable(context);
      }

      // 标记为启用
      plugin.enabled = true;

      this.emit('pluginEnabled', plugin);
    } catch (error) {
      throw new Error(`插件启用失败: ${error.message}`);
    }
  }

  /**
   * 禁用插件
   */
  public async disablePlugin(pluginId: string): Promise<void> {
    const plugin = this.plugins.get(pluginId);
    if (!plugin) {
      throw new Error(`插件不存在: ${pluginId}`);
    }

    if (!plugin.enabled) {
      return;
    }

    try {
      // 调用禁用钩子
      if (plugin.hooks?.onDisable) {
        const context = this.createPluginContext(plugin);
        await plugin.hooks.onDisable(context);
      }

      // 卸载插件代码
      this.loadedPlugins.delete(pluginId);

      // 标记为禁用
      plugin.enabled = false;

      this.emit('pluginDisabled', plugin);
    } catch (error) {
      throw new Error(`插件禁用失败: ${error.message}`);
    }
  }

  /**
   * 获取所有插件
   */
  public getPlugins(): Plugin[] {
    return Array.from(this.plugins.values());
  }

  /**
   * 获取启用的插件
   */
  public getEnabledPlugins(): Plugin[] {
    return this.getPlugins().filter(plugin => plugin.enabled);
  }

  /**
   * 获取插件实例
   */
  public getPluginInstance(pluginId: string): any {
    return this.loadedPlugins.get(pluginId);
  }

  /**
   * 调用插件钩子
   */
  public async callHook(hookName: keyof PluginHooks, ...args: any[]): Promise<void> {
    const enabledPlugins = this.getEnabledPlugins();
    
    for (const plugin of enabledPlugins) {
      const hook = plugin.hooks?.[hookName];
      if (hook) {
        try {
          const context = this.createPluginContext(plugin);
          await hook(context, ...args);
        } catch (error) {
          console.error(`插件 ${plugin.id} 的钩子 ${hookName} 执行失败:`, error);
        }
      }
    }
  }

  /**
   * 创建插件上下文
   */
  private createPluginContext(plugin: Plugin): PluginContext {
    return {
      plugin,
      editor: this.editorAPI,
      utils: this.createPluginUtils(),
      storage: this.createPluginStorage(plugin.id),
      logger: this.createPluginLogger(plugin.id)
    };
  }

  /**
   * 创建插件工具函数
   */
  private createPluginUtils(): PluginUtils {
    return {
      generateId: () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      deepClone: <T>(obj: T): T => JSON.parse(JSON.stringify(obj)),
      debounce: <T extends (...args: any[]) => any>(fn: T, delay: number): T => {
        let timeoutId: NodeJS.Timeout;
        return ((...args: any[]) => {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => fn(...args), delay);
        }) as T;
      },
      throttle: <T extends (...args: any[]) => any>(fn: T, delay: number): T => {
        let lastCall = 0;
        return ((...args: any[]) => {
          const now = Date.now();
          if (now - lastCall >= delay) {
            lastCall = now;
            return fn(...args);
          }
        }) as T;
      },
      formatDate: (date: Date, format: string): string => {
        // 简单的日期格式化实现
        return date.toLocaleDateString();
      },
      isValidJSON: (str: string): boolean => {
        try {
          JSON.parse(str);
          return true;
        } catch {
          return false;
        }
      }
    };
  }

  /**
   * 创建插件存储
   */
  private createPluginStorage(pluginId: string): PluginStorage {
    const prefix = `plugin_${pluginId}_`;
    
    return {
      get: <T = any>(key: string, defaultValue?: T): T => {
        const value = localStorage.getItem(prefix + key);
        if (value === null) {
          return defaultValue as T;
        }
        try {
          return JSON.parse(value);
        } catch {
          return value as T;
        }
      },
      set: (key: string, value: any): void => {
        localStorage.setItem(prefix + key, JSON.stringify(value));
      },
      remove: (key: string): void => {
        localStorage.removeItem(prefix + key);
      },
      clear: (): void => {
        const keys = Object.keys(localStorage).filter(key => key.startsWith(prefix));
        keys.forEach(key => localStorage.removeItem(key));
      },
      keys: (): string[] => {
        return Object.keys(localStorage)
          .filter(key => key.startsWith(prefix))
          .map(key => key.substring(prefix.length));
      }
    };
  }

  /**
   * 创建插件日志记录器
   */
  private createPluginLogger(pluginId: string): PluginLogger {
    const prefix = `[Plugin:${pluginId}]`;
    
    return {
      info: (message: string, ...args: any[]): void => {
        console.info(prefix, message, ...args);
      },
      warn: (message: string, ...args: any[]): void => {
        console.warn(prefix, message, ...args);
      },
      error: (message: string, ...args: any[]): void => {
        console.error(prefix, message, ...args);
      },
      debug: (message: string, ...args: any[]): void => {
        console.debug(prefix, message, ...args);
      }
    };
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: Plugin): void {
    if (!plugin.id || !plugin.name || !plugin.version) {
      throw new Error('插件缺少必要的元数据');
    }

    if (this.plugins.has(plugin.id)) {
      throw new Error(`插件 ${plugin.id} 已存在`);
    }
  }

  /**
   * 检查依赖
   */
  private async checkDependencies(plugin: Plugin): Promise<void> {
    if (!plugin.dependencies) {
      return;
    }

    for (const dependency of plugin.dependencies) {
      if (!this.plugins.has(dependency)) {
        throw new Error(`缺少依赖插件: ${dependency}`);
      }
    }
  }

  /**
   * 从源加载插件
   */
  private async loadPluginFromSource(source: string): Promise<Plugin> {
    // 这里应该实现从URL或文件加载插件的逻辑
    throw new Error('从源加载插件功能尚未实现');
  }

  /**
   * 加载插件代码
   */
  private async loadPluginCode(plugin: Plugin): Promise<any> {
    // 这里应该实现动态加载插件代码的逻辑
    // 可以使用 import() 或其他动态加载机制
    return {};
  }

  /**
   * 加载已安装的插件
   */
  private async loadInstalledPlugins(): Promise<void> {
    // 这里应该从存储中加载已安装的插件列表
    // 并自动启用已启用的插件
  }
}

// 导出单例实例
export const pluginSystem = PluginSystem.getInstance();
