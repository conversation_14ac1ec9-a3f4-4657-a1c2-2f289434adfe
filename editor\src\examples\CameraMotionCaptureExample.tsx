/**
 * 摄像头动作捕捉功能示例
 * 展示如何在编辑器中集成和使用摄像头动作捕捉功能
 */
import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Space,
  Button,
  Alert,
  Statistic,
  Progress,
  Tag,
  Typography,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CameraOutlined,
  RobotOutlined,
  HandIcon
} from '@ant-design/icons';
import CameraMotionCapturePanel from '../components/mocap/CameraMotionCapturePanel';

const { Title, Text } = Typography;

/**
 * 摄像头动作捕捉示例组件
 */
const CameraMotionCaptureExample: React.FC = () => {
  // 状态管理
  const [isRunning, setIsRunning] = useState(false);
  const [stats, setStats] = useState({
    fps: 0,
    poseDetectionRate: 0,
    handDetectionRate: 0,
    processingTime: 0
  });
  const [detectedGestures, setDetectedGestures] = useState<string[]>([]);
  const [interactionEvents, setInteractionEvents] = useState<string[]>([]);
  
  // 引用
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const systemRef = useRef<any>(null);

  /**
   * 初始化摄像头动作捕捉系统
   */
  const initializeSystem = async () => {
    try {
      // 这里应该初始化实际的CameraMotionCaptureSystem
      // const system = new CameraMotionCaptureSystem(world, config);
      // await system.initialize();
      // systemRef.current = system;
      
      console.log('摄像头动作捕捉系统初始化成功');
    } catch (error) {
      console.error('系统初始化失败:', error);
    }
  };

  /**
   * 启动动作捕捉
   */
  const startMotionCapture = async () => {
    try {
      if (!systemRef.current) {
        await initializeSystem();
      }
      
      // await systemRef.current.enable();
      setIsRunning(true);
      
      // 模拟数据更新
      startStatsUpdate();
      
    } catch (error) {
      console.error('启动动作捕捉失败:', error);
    }
  };

  /**
   * 停止动作捕捉
   */
  const stopMotionCapture = async () => {
    try {
      // if (systemRef.current) {
      //   await systemRef.current.disable();
      // }
      
      setIsRunning(false);
      
    } catch (error) {
      console.error('停止动作捕捉失败:', error);
    }
  };

  /**
   * 开始统计数据更新（模拟）
   */
  const startStatsUpdate = () => {
    const interval = setInterval(() => {
      if (!isRunning) {
        clearInterval(interval);
        return;
      }
      
      // 模拟统计数据
      setStats({
        fps: 25 + Math.random() * 10,
        poseDetectionRate: 0.8 + Math.random() * 0.2,
        handDetectionRate: 0.7 + Math.random() * 0.3,
        processingTime: 15 + Math.random() * 10
      });
      
      // 模拟手势检测
      const gestures = ['抓取', '张开', '指向', '竖拇指'];
      if (Math.random() > 0.7) {
        const gesture = gestures[Math.floor(Math.random() * gestures.length)];
        setDetectedGestures(prev => {
          const newGestures = [gesture, ...prev.slice(0, 4)];
          return newGestures;
        });
      }
      
      // 模拟交互事件
      const interactions = ['抓取物体', '释放物体', '指向目标', '手势命令'];
      if (Math.random() > 0.8) {
        const interaction = interactions[Math.floor(Math.random() * interactions.length)];
        setInteractionEvents(prev => {
          const newEvents = [`${new Date().toLocaleTimeString()}: ${interaction}`, ...prev.slice(0, 9)];
          return newEvents;
        });
      }
      
    }, 100);
  };

  /**
   * 处理配置变化
   */
  const handleConfigChange = (config: any) => {
    console.log('配置更新:', config);
    // 这里应该更新实际的系统配置
    // if (systemRef.current) {
    //   systemRef.current.updateConfig(config);
    // }
  };

  /**
   * 处理启用状态变化
   */
  const handleEnabledChange = (enabled: boolean) => {
    if (enabled) {
      startMotionCapture();
    } else {
      stopMotionCapture();
    }
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (systemRef.current) {
        systemRef.current.destroy();
      }
    };
  }, []);

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>
        <CameraOutlined /> 摄像头动作捕捉功能演示
      </Title>
      
      <Alert
        message="功能说明"
        description="此演示展示了如何使用摄像头进行实时动作捕捉，包括姿态识别、手部追踪、手势识别和虚拟交互映射。"
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        {/* 左侧：控制面板 */}
        <Col span={8}>
          <CameraMotionCapturePanel
            onConfigChange={handleConfigChange}
            onEnabledChange={handleEnabledChange}
          />
          
          <Card title="系统控制" size="small" style={{ marginTop: 16 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type={isRunning ? "default" : "primary"}
                icon={isRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={isRunning ? stopMotionCapture : startMotionCapture}
                block
              >
                {isRunning ? '停止捕捉' : '开始捕捉'}
              </Button>
              
              <Button
                onClick={() => {
                  setDetectedGestures([]);
                  setInteractionEvents([]);
                }}
                block
              >
                清除记录
              </Button>
            </Space>
          </Card>
        </Col>

        {/* 中间：视频预览和统计 */}
        <Col span={10}>
          <Card title="视频预览" size="small">
            <div style={{ 
              width: '100%', 
              height: 300, 
              backgroundColor: '#f0f0f0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px dashed #d9d9d9',
              borderRadius: 8
            }}>
              <canvas
                ref={canvasRef}
                width={640}
                height={480}
                style={{ maxWidth: '100%', maxHeight: '100%' }}
              />
              {!isRunning && (
                <div style={{ textAlign: 'center', color: '#999' }}>
                  <CameraOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                  <div>摄像头预览区域</div>
                  <div>点击"开始捕捉"启动</div>
                </div>
              )}
            </div>
          </Card>

          <Card title="性能统计" size="small" style={{ marginTop: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="FPS"
                  value={stats.fps}
                  precision={1}
                  suffix="fps"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="处理时间"
                  value={stats.processingTime}
                  precision={1}
                  suffix="ms"
                />
              </Col>
            </Row>
            
            <Divider />
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>姿态检测成功率</Text>
              <Progress 
                percent={stats.poseDetectionRate * 100} 
                size="small"
                status={stats.poseDetectionRate > 0.8 ? "success" : "normal"}
              />
            </div>
            
            <div>
              <Text strong>手部检测成功率</Text>
              <Progress 
                percent={stats.handDetectionRate * 100} 
                size="small"
                status={stats.handDetectionRate > 0.7 ? "success" : "normal"}
              />
            </div>
          </Card>
        </Col>

        {/* 右侧：检测结果 */}
        <Col span={6}>
          <Card title="检测到的手势" size="small">
            <div style={{ maxHeight: 200, overflowY: 'auto' }}>
              {detectedGestures.length === 0 ? (
                <Text type="secondary">暂无手势检测</Text>
              ) : (
                detectedGestures.map((gesture, index) => (
                  <Tag 
                    key={index} 
                    color="blue" 
                    style={{ marginBottom: 8, display: 'block' }}
                  >
                    <HandIcon /> {gesture}
                  </Tag>
                ))
              )}
            </div>
          </Card>

          <Card title="交互事件" size="small" style={{ marginTop: 16 }}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {interactionEvents.length === 0 ? (
                <Text type="secondary">暂无交互事件</Text>
              ) : (
                interactionEvents.map((event, index) => (
                  <div 
                    key={index} 
                    style={{ 
                      padding: '4px 8px', 
                      marginBottom: 4, 
                      backgroundColor: '#f6f6f6',
                      borderRadius: 4,
                      fontSize: 12
                    }}
                  >
                    <RobotOutlined style={{ marginRight: 4, color: '#1890ff' }} />
                    {event}
                  </div>
                ))
              )}
            </div>
          </Card>

          <Card title="虚拟化身状态" size="small" style={{ marginTop: 16 }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text strong>左手状态: </Text>
                <Tag color={Math.random() > 0.5 ? "green" : "default"}>
                  {Math.random() > 0.5 ? "抓取中" : "空闲"}
                </Tag>
              </div>
              <div>
                <Text strong>右手状态: </Text>
                <Tag color={Math.random() > 0.5 ? "green" : "default"}>
                  {Math.random() > 0.5 ? "抓取中" : "空闲"}
                </Tag>
              </div>
              <div>
                <Text strong>姿态状态: </Text>
                <Tag color="blue">正常站立</Tag>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 底部：使用说明 */}
      <Card title="使用说明" style={{ marginTop: 24 }}>
        <Row gutter={24}>
          <Col span={8}>
            <Title level={4}>基础操作</Title>
            <ul>
              <li>点击"开始捕捉"启动摄像头动作捕捉</li>
              <li>确保摄像头权限已授权</li>
              <li>保持良好的光线条件</li>
              <li>身体完全在摄像头视野内</li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={4}>支持的手势</Title>
            <ul>
              <li><strong>抓取:</strong> 握拳动作，用于抓取虚拟物体</li>
              <li><strong>张开:</strong> 张开手掌，用于释放物体</li>
              <li><strong>指向:</strong> 食指指向，用于选择目标</li>
              <li><strong>竖拇指:</strong> 竖拇指手势，用于确认操作</li>
            </ul>
          </Col>
          <Col span={8}>
            <Title level={4}>虚拟交互</Title>
            <ul>
              <li>手势识别会自动映射到虚拟化身</li>
              <li>可以抓取和搬运虚拟物体</li>
              <li>支持多人协作场景</li>
              <li>实时同步动作数据</li>
            </ul>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default CameraMotionCaptureExample;
