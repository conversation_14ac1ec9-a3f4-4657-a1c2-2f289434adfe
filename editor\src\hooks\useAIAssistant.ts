/**
 * AI助手Hook
 * 提供AI助手相关的功能和状态管理
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { AIService } from '../services/AIService';
import { ChatMessage, AIAction } from '../components/ai/AIChatPanel';

// 用户意图类型
export interface UserIntent {
  type: IntentType;
  confidence: number;
  entities: IntentEntity[];
  parameters: Record<string, any>;
  context: string[];
}

export enum IntentType {
  CREATE_OBJECT = 'create_object',
  MODIFY_OBJECT = 'modify_object',
  DELETE_OBJECT = 'delete_object',
  GENERATE_CODE = 'generate_code',
  ANALYZE_SCENE = 'analyze_scene',
  OPTIMIZE_PERFORMANCE = 'optimize_performance',
  RECOMMEND_ASSETS = 'recommend_assets',
  HELP_REQUEST = 'help_request',
  GENERAL_QUESTION = 'general_question',
  UNKNOWN = 'unknown'
}

export interface IntentEntity {
  type: string;
  value: string;
  confidence: number;
  start: number;
  end: number;
}

// AI响应
export interface AIResponse {
  text: string;
  actions?: AIAction[];
  suggestions?: string[];
  model: string;
  confidence: number;
  processingTime: number;
  tokens: number;
  metadata?: Record<string, any>;
}

// AI助手配置
export interface AIAssistantConfig {
  apiEndpoint?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  retryAttempts?: number;
  enableCache?: boolean;
  enableAnalytics?: boolean;
}

// AI助手状态
export interface AIAssistantState {
  isConnected: boolean;
  isProcessing: boolean;
  currentModel: string;
  usage: {
    totalRequests: number;
    totalTokens: number;
    averageResponseTime: number;
  };
  capabilities: string[];
  lastError?: string;
}

/**
 * AI助手Hook
 */
export function useAIAssistant(config: AIAssistantConfig = {}) {
  // 状态
  const [state, setState] = useState<AIAssistantState>({
    isConnected: false,
    isProcessing: false,
    currentModel: config.model || 'gpt-3.5-turbo',
    usage: {
      totalRequests: 0,
      totalTokens: 0,
      averageResponseTime: 0
    },
    capabilities: [
      'scene_creation',
      'code_generation',
      'performance_analysis',
      'asset_recommendation',
      'natural_language_query'
    ]
  });

  // 服务实例
  const aiServiceRef = useRef<AIService | null>(null);
  
  // 请求缓存
  const cacheRef = useRef<Map<string, AIResponse>>(new Map());
  
  // 请求队列
  const requestQueueRef = useRef<Array<{
    id: string;
    request: any;
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }>>([]);

  // 初始化AI服务
  useEffect(() => {
    const initializeAIService = async () => {
      try {
        aiServiceRef.current = new AIService({
          apiEndpoint: config.apiEndpoint || '/api/ai',
          model: config.model || 'gpt-3.5-turbo',
          maxTokens: config.maxTokens || 2048,
          temperature: config.temperature || 0.7,
          timeout: config.timeout || 30000,
          retryAttempts: config.retryAttempts || 3
        });

        await aiServiceRef.current.initialize();

        setState(prev => ({
          ...prev,
          isConnected: true,
          lastError: undefined
        }));

      } catch (error) {
        console.error('AI服务初始化失败:', error);
        setState(prev => ({
          ...prev,
          isConnected: false,
          lastError: (error as Error).message
        }));
        message.error('AI助手连接失败，请检查网络连接');
      }
    };

    initializeAIService();

    // 清理函数
    return () => {
      if (aiServiceRef.current) {
        aiServiceRef.current.destroy();
      }
    };
  }, [config]);

  /**
   * 分析用户意图
   */
  const analyzeIntent = useCallback(async (input: string): Promise<UserIntent> => {
    if (!aiServiceRef.current) {
      throw new Error('AI服务未初始化');
    }

    const startTime = Date.now();

    try {
      setState(prev => ({ ...prev, isProcessing: true }));

      const result = await aiServiceRef.current.analyzeIntent(input);

      // 更新使用统计
      const processingTime = Date.now() - startTime;
      setState(prev => ({
        ...prev,
        isProcessing: false,
        usage: {
          totalRequests: prev.usage.totalRequests + 1,
          totalTokens: prev.usage.totalTokens + (result.tokens || 0),
          averageResponseTime: (prev.usage.averageResponseTime * prev.usage.totalRequests + processingTime) / (prev.usage.totalRequests + 1)
        }
      }));

      return result;

    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        lastError: (error as Error).message
      }));
      throw error;
    }
  }, []);

  /**
   * 生成AI响应
   */
  const generateResponse = useCallback(async (
    intent: UserIntent,
    context: {
      context: any;
      history: ChatMessage[];
      userId: string;
    }
  ): Promise<AIResponse> => {
    if (!aiServiceRef.current) {
      throw new Error('AI服务未初始化');
    }

    const startTime = Date.now();

    try {
      setState(prev => ({ ...prev, isProcessing: true }));

      // 检查缓存
      const cacheKey = generateCacheKey(intent, context);
      if (config.enableCache && cacheRef.current.has(cacheKey)) {
        const cachedResponse = cacheRef.current.get(cacheKey)!;
        setState(prev => ({ ...prev, isProcessing: false }));
        return cachedResponse;
      }

      const response = await aiServiceRef.current.generateResponse(intent, context);

      // 缓存响应
      if (config.enableCache) {
        cacheRef.current.set(cacheKey, response);
        
        // 限制缓存大小
        if (cacheRef.current.size > 100) {
          const firstKey = cacheRef.current.keys().next().value;
          cacheRef.current.delete(firstKey);
        }
      }

      // 更新使用统计
      const processingTime = Date.now() - startTime;
      setState(prev => ({
        ...prev,
        isProcessing: false,
        usage: {
          totalRequests: prev.usage.totalRequests + 1,
          totalTokens: prev.usage.totalTokens + response.tokens,
          averageResponseTime: (prev.usage.averageResponseTime * prev.usage.totalRequests + processingTime) / (prev.usage.totalRequests + 1)
        }
      }));

      return response;

    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        lastError: (error as Error).message
      }));
      throw error;
    }
  }, [config.enableCache]);

  /**
   * 提交用户反馈
   */
  const submitFeedback = useCallback(async (
    messageId: string,
    feedback: 'positive' | 'negative',
    comment?: string
  ): Promise<void> => {
    if (!aiServiceRef.current) {
      throw new Error('AI服务未初始化');
    }

    try {
      await aiServiceRef.current.submitFeedback({
        messageId,
        feedback,
        comment,
        timestamp: Date.now()
      });

      message.success('反馈已提交，谢谢！');

    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('反馈提交失败，请稍后重试');
      throw error;
    }
  }, []);

  /**
   * 获取AI能力列表
   */
  const getCapabilities = useCallback(async (): Promise<string[]> => {
    if (!aiServiceRef.current) {
      return state.capabilities;
    }

    try {
      const capabilities = await aiServiceRef.current.getCapabilities();
      setState(prev => ({ ...prev, capabilities }));
      return capabilities;
    } catch (error) {
      console.error('获取AI能力失败:', error);
      return state.capabilities;
    }
  }, [state.capabilities]);

  /**
   * 切换AI模型
   */
  const switchModel = useCallback(async (modelName: string): Promise<void> => {
    if (!aiServiceRef.current) {
      throw new Error('AI服务未初始化');
    }

    try {
      setState(prev => ({ ...prev, isProcessing: true }));

      await aiServiceRef.current.switchModel(modelName);

      setState(prev => ({
        ...prev,
        currentModel: modelName,
        isProcessing: false
      }));

      // 清空缓存（因为模型变了）
      cacheRef.current.clear();

      message.success(`已切换到模型: ${modelName}`);

    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        lastError: (error as Error).message
      }));
      message.error(`模型切换失败: ${(error as Error).message}`);
      throw error;
    }
  }, []);

  /**
   * 重新连接AI服务
   */
  const reconnect = useCallback(async (): Promise<void> => {
    if (!aiServiceRef.current) {
      return;
    }

    try {
      setState(prev => ({ ...prev, isProcessing: true }));

      await aiServiceRef.current.reconnect();

      setState(prev => ({
        ...prev,
        isConnected: true,
        isProcessing: false,
        lastError: undefined
      }));

      message.success('AI助手重新连接成功');

    } catch (error) {
      setState(prev => ({
        ...prev,
        isConnected: false,
        isProcessing: false,
        lastError: (error as Error).message
      }));
      message.error('重新连接失败');
      throw error;
    }
  }, []);

  /**
   * 清空缓存
   */
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
    message.success('缓存已清空');
  }, []);

  /**
   * 获取使用统计
   */
  const getUsageStats = useCallback(() => {
    return state.usage;
  }, [state.usage]);

  // 辅助函数
  const generateCacheKey = (intent: UserIntent, context: any): string => {
    const key = `${intent.type}:${JSON.stringify(intent.parameters)}:${JSON.stringify(context.context)}`;
    return Buffer.from(key).toString('base64').substring(0, 32);
  };

  return {
    // 状态
    state,
    
    // 方法
    analyzeIntent,
    generateResponse,
    submitFeedback,
    getCapabilities,
    switchModel,
    reconnect,
    clearCache,
    getUsageStats,
    
    // 计算属性
    isReady: state.isConnected && !state.isProcessing,
    hasError: !!state.lastError
  };
}
