/**
 * NFT Hook - 提供NFT相关的状态管理和操作
 */

import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { EngineService } from '../services/EngineService';

export interface NFTToken {
  tokenId: string;
  contractAddress: string;
  chainId: number;
  owner: string;
  creator: string;
  metadata: NFTMetadata;
  tokenURI: string;
  mintedAt: Date;
  lastTransferAt?: Date;
  transferCount: number;
  isForSale: boolean;
  price?: string;
  currency?: string;
}

export interface NFTMetadata {
  name: string;
  description: string;
  image: string;
  external_url?: string;
  attributes: NFTAttribute[];
  animation_url?: string;
  dl_engine_data: {
    scene_id?: string;
    asset_type: string;
    engine_version: string;
    creation_timestamp: number;
    creator_address: string;
    license_type: string;
    educational_metadata?: any;
    technical_metadata?: any;
    interaction_metadata?: any;
  };
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
  display_type?: string;
  max_value?: number;
}

export interface NFTSearchFilter {
  category?: string;
  assetType?: string;
  priceRange?: {
    min: string;
    max: string;
    currency: string;
  };
  creator?: string;
  owner?: string;
  traits?: Record<string, string | number>;
  sortBy?: 'price' | 'created' | 'popularity' | 'name';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface UseNFTReturn {
  nfts: NFTToken[];
  isLoading: boolean;
  error: string | null;
  loadUserNFTs: () => Promise<void>;
  searchNFTs: (filter: NFTSearchFilter) => Promise<NFTToken[]>;
  mintNFT: (assetId: string, metadata: NFTMetadata, royaltyRecipient?: string, royaltyPercentage?: number) => Promise<NFTToken>;
  transferNFT: (tokenId: string, contractAddress: string, toAddress: string) => Promise<string>;
  displayNFT: (tokenId: string, contractAddress: string, position?: { x: number; y: number; z: number }) => Promise<void>;
  hideNFT: (tokenId: string, contractAddress: string) => Promise<void>;
  getNFTInfo: (tokenId: string, contractAddress: string) => Promise<NFTToken | null>;
}

export const useNFT = (): UseNFTReturn => {
  const [nfts, setNfts] = useState<NFTToken[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取区块链系统
  const getBlockchainSystem = useCallback(() => {
    const engine = EngineService.getEngine();
    return engine?.getSystem('BlockchainSystem');
  }, []);

  // 获取NFT管理器
  const getNFTManager = useCallback(() => {
    const blockchainSystem = getBlockchainSystem();
    return blockchainSystem?.getNFTManager();
  }, [getBlockchainSystem]);

  // 加载用户NFT
  const loadUserNFTs = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) {
        throw new Error('区块链系统未初始化');
      }

      const userNFTs = blockchainSystem.getUserNFTs();
      setNfts(userNFTs);
    } catch (error: any) {
      const errorMessage = error.message || '加载NFT失败';
      setError(errorMessage);
      console.error('加载用户NFT失败:', error);
    } finally {
      setIsLoading(false);
    }
  }, [getBlockchainSystem]);

  // 搜索NFT
  const searchNFTs = useCallback(async (filter: NFTSearchFilter): Promise<NFTToken[]> => {
    try {
      const nftManager = getNFTManager();
      if (!nftManager) {
        throw new Error('NFT管理器未初始化');
      }

      const results = await nftManager.searchNFTs(filter);
      return results;
    } catch (error: any) {
      const errorMessage = error.message || '搜索NFT失败';
      setError(errorMessage);
      console.error('搜索NFT失败:', error);
      return [];
    }
  }, [getNFTManager]);

  // 铸造NFT
  const mintNFT = useCallback(async (
    assetId: string,
    metadata: NFTMetadata,
    royaltyRecipient?: string,
    royaltyPercentage?: number
  ): Promise<NFTToken> => {
    setIsLoading(true);
    setError(null);

    try {
      const nftManager = getNFTManager();
      if (!nftManager) {
        throw new Error('NFT管理器未初始化');
      }

      const result = await nftManager.mintNFT(assetId, metadata, royaltyRecipient, royaltyPercentage);
      
      if (result.success && result.data) {
        // 更新本地NFT列表
        setNfts(prev => [result.data!, ...prev]);
        message.success('NFT铸造成功！');
        return result.data;
      } else {
        throw new Error(result.error || 'NFT铸造失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'NFT铸造失败';
      setError(errorMessage);
      message.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [getNFTManager]);

  // 转移NFT
  const transferNFT = useCallback(async (
    tokenId: string,
    contractAddress: string,
    toAddress: string
  ): Promise<string> => {
    setIsLoading(true);
    setError(null);

    try {
      const nftManager = getNFTManager();
      if (!nftManager) {
        throw new Error('NFT管理器未初始化');
      }

      const result = await nftManager.transferNFT(tokenId, contractAddress, toAddress);
      
      if (result.success && result.transactionHash) {
        // 更新本地NFT列表中的所有者信息
        setNfts(prev => prev.map(nft => {
          if (nft.tokenId === tokenId && nft.contractAddress === contractAddress) {
            return {
              ...nft,
              owner: toAddress,
              lastTransferAt: new Date(),
              transferCount: nft.transferCount + 1
            };
          }
          return nft;
        }));

        message.success('NFT转移成功！');
        return result.transactionHash;
      } else {
        throw new Error(result.error || 'NFT转移失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || 'NFT转移失败';
      setError(errorMessage);
      message.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [getNFTManager]);

  // 在场景中显示NFT
  const displayNFT = useCallback(async (
    tokenId: string,
    contractAddress: string,
    position?: { x: number; y: number; z: number }
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const blockchainSystem = getBlockchainSystem();
      if (!blockchainSystem) {
        throw new Error('区块链系统未初始化');
      }

      const entity = await blockchainSystem.displayNFTEntity(tokenId, contractAddress, position);
      
      if (entity) {
        message.success('NFT已在场景中显示');
      } else {
        throw new Error('显示NFT失败');
      }
    } catch (error: any) {
      const errorMessage = error.message || '显示NFT失败';
      setError(errorMessage);
      message.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [getBlockchainSystem]);

  // 隐藏NFT
  const hideNFT = useCallback(async (tokenId: string, contractAddress: string) => {
    try {
      const nftManager = getNFTManager();
      if (!nftManager) {
        throw new Error('NFT管理器未初始化');
      }

      await nftManager.hideNFT(tokenId, contractAddress);
      message.success('NFT已从场景中隐藏');
    } catch (error: any) {
      const errorMessage = error.message || '隐藏NFT失败';
      setError(errorMessage);
      message.error(errorMessage);
      throw error;
    }
  }, [getNFTManager]);

  // 获取NFT信息
  const getNFTInfo = useCallback(async (
    tokenId: string,
    contractAddress: string
  ): Promise<NFTToken | null> => {
    try {
      const nftManager = getNFTManager();
      if (!nftManager) {
        throw new Error('NFT管理器未初始化');
      }

      const nft = await nftManager.getNFTInfo(tokenId, contractAddress);
      return nft;
    } catch (error: any) {
      console.error('获取NFT信息失败:', error);
      return null;
    }
  }, [getNFTManager]);

  // 监听区块链系统事件
  useEffect(() => {
    const blockchainSystem = getBlockchainSystem();
    if (!blockchainSystem) return;

    const handleNFTMinted = (nft: NFTToken) => {
      setNfts(prev => {
        // 检查是否已存在，避免重复添加
        const exists = prev.some(n => n.tokenId === nft.tokenId && n.contractAddress === nft.contractAddress);
        if (!exists) {
          return [nft, ...prev];
        }
        return prev;
      });
    };

    const handleNFTTransferred = (data: any) => {
      setNfts(prev => prev.map(nft => {
        if (nft.tokenId === data.tokenId && nft.contractAddress === data.contractAddress) {
          return {
            ...nft,
            owner: data.to,
            lastTransferAt: new Date(),
            transferCount: nft.transferCount + 1
          };
        }
        return nft;
      }));
    };

    blockchainSystem.on('nftMinted', handleNFTMinted);
    blockchainSystem.on('nftTransferred', handleNFTTransferred);

    return () => {
      blockchainSystem.off('nftMinted', handleNFTMinted);
      blockchainSystem.off('nftTransferred', handleNFTTransferred);
    };
  }, [getBlockchainSystem]);

  // 初始化时加载用户NFT
  useEffect(() => {
    const blockchainSystem = getBlockchainSystem();
    if (blockchainSystem) {
      const state = blockchainSystem.getBlockchainState();
      if (state.isConnected) {
        loadUserNFTs();
      }
    }
  }, [getBlockchainSystem, loadUserNFTs]);

  return {
    nfts,
    isLoading,
    error,
    loadUserNFTs,
    searchNFTs,
    mintNFT,
    transferNFT,
    displayNFT,
    hideNFT,
    getNFTInfo
  };
};
